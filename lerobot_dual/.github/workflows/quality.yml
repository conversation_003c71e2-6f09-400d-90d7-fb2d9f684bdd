# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Quality

on:
  workflow_dispatch:
  workflow_call:
  pull_request:
  push:
    branches:
      - main

permissions: {}

env:
  PYTHON_VERSION: "3.10"

jobs:
  style:
    name: Style
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Get Ruff Version from pre-commit-config.yaml
        id: get-ruff-version
        run: |
          RUFF_VERSION=$(awk '/repo: https:\/\/github.com\/astral-sh\/ruff-pre-commit/{flag=1;next}/rev:/{if(flag){print $2;exit}}' .pre-commit-config.yaml)
          echo "ruff_version=${RUFF_VERSION}" >> $GITHUB_OUTPUT

      - name: Install Ruff
        env:
          RUFF_VERSION: ${{ steps.get-ruff-version.outputs.ruff_version }}
        run: python -m pip install "ruff==${RUFF_VERSION}"

      - name: Ruff check
        run: ruff check --output-format=github

      - name: Ruff format
        run: ruff format --diff

  typos:
    name: Typos
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: typos-action
        uses: crate-ci/typos@v1.29.10
