
# LeRobot 数据集结构详解

本文档旨在详细解释使用 `lerobot` 框架，特别是通过 `control_robot.py` 脚本录制的机器人数据集的内部结构和组织逻辑。本文面向开发者、研究人员与对机器人技术感兴趣的初学者，帮助理解数据被捕获、存储和组织的方式。

## 一、 数据录制流程概览

当在终端执行一个类似下面的录制命令时，`lerobot` 框架会启动一个流程来捕获机器人的所有行为和感知数据。

```bash
python lerobot/scripts/control_robot.py \
    --robot.type so101 \
    --control.type record \
    --control.fps 30 \
    ...
```

整个流程可以概括为以下几个步骤：

1.  **初始化**: 脚本首先会解析输入的所有参数，并根据这些参数（如机器人类型、录制帧率等）创建一个数据集的配置。
2.  **创建数据集实例**: 系统会在本地缓存目录（通常是 `~/.cache/huggingface/lerobot/`）下，根据指定的仓库ID（`--control.repo_id`）创建一个新的文件夹。这个文件夹是数据集的根目录。
3.  **分集录制 (Recording in Episodes)**: 机器人的一次完整操作（例如，从开始到完成一个任务）被称为一个“片段” (`episode`)。脚本会进入一个循环，一集一集地进行录制。
4.  **实时数据捕获**: 在每一集的录制过程中，系统会以指定的帧率（例如每秒30次）从机器人的各个部分捕获数据：
    *   **视觉数据**: 摄像头捕捉到的图像。
    *   **物理数据**: 机器人关节的角度、速度（状态 `state`）以及通过手柄等设备发出的控制指令（动作 `action`）。
5.  **临时存储**:
    *   图像帧会暂时存为独立的 `.png` 图片文件。
    *   物理数据（动作、状态等）则暂存在内存的一个缓冲区中。
6.  **数据归档**: 当一集录制结束后，系统会进行归档处理：
    *   内存中的物理数据被整理并保存到一个 `.parquet` 文件中。
    *   临时的 `.png` 图片会被编码成一个 `.mp4` 视频文件。
    *   描述这一集信息的元数据（如时长、执行的任务等）会被更新到对应的 `meta` 文件中。
    *   最后，临时的 `.png` 文件被删除，以节省空间。

通过这个流程，`lerobot` 确保了数据的有序和完整存储。

## 二、 数据集文件结构

一个标准的 `lerobot` 数据集在文件系统中会呈现出以下层级结构：

```
/path/to/your/dataset/ (例如: ~/.cache/huggingface/lerobot/fibot/so101_dual_100/)
├── data/
│   └── chunk-000/
│       ├── episode_000000.parquet
│       ├── episode_000001.parquet
│       └── ...
├── meta/
│   ├── episodes.jsonl
│   ├── episodes_stats.jsonl
│   ├── info.json
│   ├── tasks.jsonl
│   └── README.md
└── videos/
    └── chunk-000/
        ├── observation.images.top/
        │   ├── episode_000000.mp4
        │   └── ...
        └── ... (其他摄像头)
```

*   `data/`: **核心数据目录**。这里存放着机器人每一帧的详细物理数据。为了便于管理，数据被分块 (`chunk`) 存放，每个 `chunk` 默认包含1000个 `episode` 的文件。
*   `meta/`: **元数据目录**。这里存放的是“描述数据的数据”，即数据集的“说明书”和“目录”，用于理解和使用数据。
*   `videos/`: **视觉数据目录**。这里存放着机器人摄像头所看到的所有画面的视频文件。

## 三、 核心文件详解

下面逐一解析每个重要文件所包含的信息。

### `data/` 目录 - 核心物理数据

#### `episode_*.parquet` 文件

*   **格式**: Apache Parquet，一种高效的列式存储格式，读取速度快，占用空间小，适合大规模数据分析。
*   **内容**: 记录了除图像外所有关键的、结构化的数据。每一行代表一帧数据，每一列代表一个具体的特征。主要包含：
    *   `action`: 记录了发送给机器人的控制指令，例如手臂关节要移动到的目标角度。它的**数据维度 (Shape)** 通常是 `(action_dim,)`，`action_dim` 的大小取决于机器人可独立控制的关节数量。
    *   `state`: 记录了机器人自身的状态，主要是各个关节的实际位置、速度等。它的**数据维度 (Shape)** 通常是 `(state_dim,)`。
    *   `timestamp`: 这一帧数据被记录下来的精确时间戳。
    *   `frame_index`: 这一帧在本集内的序号。
    *   `episode_index`: 当前是第几集。
    *   `task_index`: 当前执行的是哪个任务的索引号。
*   **逻辑架构**: 将物理数据和视觉数据分开存储，使得可以独立地分析机器人的动作与状态，而无需加载庞大的视频文件，提高了数据处理效率。

### `videos/` 目录 - 视觉数据

#### `episode_*.mp4` 文件

*   **格式**: MP4 视频文件。
*   **内容**: 对应一个摄像头在一个 `episode` 中录制的所有画面, 由录制过程中产生的临时 `.png` 图片序列编码而成。
*   **数据维度 (Shape)**: 视频可以被看作一个四维数组：`(帧数, 高度, 宽度, 色彩通道数)`。例如，`(600, 480, 640, 3)` 就表示这段视频共有600帧，每一帧的图像分辨率是 480x640，并且是3通道的彩色图像（RGB）。

### `meta/` 目录 - 元数据

#### `info.json`

*   **格式**: JSON。
*   **内容**: 包含数据集的核心配置信息。
    *   `fps`: 录制时的帧率（每秒多少帧）。
    *   `robot_type`: 使用的机器人型号。
    *   `features`: 一个重要的字典，详细定义了数据集中所有数据字段的名称、数据类型 (`dtype`) 和**数据维度 (Shape)**。它定义了 `action` 是一个包含7个浮点数的数组，`state` 是一个包含14个浮点数的数组等。

#### `episodes.jsonl`

*   **格式**: JSON Lines，每一行是一个独立的JSON对象。
*   **内容**: `episode` 的目录索引，每一行记录一集的信息，包括 `episode_index` (第几集), `tasks` (这集对应的任务描述) 和 `length` (这集总共有多少帧)。

#### `episodes_stats.jsonl`

*   **格式**: JSON Lines。
*   **内容**: 每一集的统计摘要。计算了每集中 `action`、`state` 等数值型数据的均值、标准差等统计量。这些信息在训练机器学习模型时可用于数据归一化。

#### `tasks.jsonl`

*   **格式**: JSON Lines。
*   **内容**: 任务的映射表。将人类可读的任务描述（如 "Grasp a yellow cube and put it in a box."）映射到一个唯一的数字索引 (`task_index`)。在数据文件中仅需存储数字索引，而不是重复存储冗长的字符串。

## 总结

通过将物理数据、视觉数据和元数据分离存储，并采用标准化的文件格式（Parquet, MP4, JSON），`lerobot` 的数据结构使得数据易于管理和使用，并能与 Hugging Face Hub 等数据平台集成，以促进机器人学习社区的合作与发展。 