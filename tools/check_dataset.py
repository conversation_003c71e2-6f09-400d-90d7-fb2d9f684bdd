#!/usr/bin/env python3

import os
import subprocess
import argparse


'''
python check_dataset.py --dataset /data/test/dataset

find /data/test/so101_dual_55/videos -type f -name "*.mp4" -print -exec ffmpeg -v error -i {} -f null - \;

'''

def process_videos(dataset_path):
    """
    Search for all .mp4 files in the specified path and check them with ffmpeg, printing only files with errors.
    
    Args:
        dataset_path (str): Absolute path to the dataset
    """
    # Check if the path exists
    if not os.path.exists(dataset_path):
        print(f"Error: Path {dataset_path} does not exist")
        return

    # Walk through the directory to find .mp4 files
    for root, _, files in os.walk(dataset_path):
        for file in files:
            if file.endswith(".mp4"):
                file_path = os.path.join(root, file)
                
                # Build ffmpeg command
                ffmpeg_cmd = [
                    "ffmpeg",
                    "-v", "error",
                    "-i", file_path,
                    "-f", "null",
                    "-"
                ]
                
                # Execute ffmpeg command and capture output
                result = subprocess.run(
                    ffmpeg_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                # Check if stderr contains any error messages
                if result.stderr.strip():
                    print(f"Error file: {file_path}")
                    print(f"Error message: {result.stderr.strip()}")
                    print("-" * 50)

def main():
    # Set up command-line argument parsing
    parser = argparse.ArgumentParser(description="Process MP4 files in the specified path, printing only error files")
    parser.add_argument(
        "--dataset",
        type=str,
        required=True,
        help="Absolute path to the dataset"
    )
    
    args = parser.parse_args()
    
    # Process video files
    process_videos(args.dataset)

if __name__ == "__main__":
    main()