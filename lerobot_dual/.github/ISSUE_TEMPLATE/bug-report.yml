# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: "\U0001F41B Bug Report"
description: Submit a bug report to help us improve LeRobot
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to submit a bug report! 🐛
        If this is not a bug related to the LeRobot library directly, but instead a general question about your code or the library specifically please use our [discord](https://discord.gg/s3KuuzsPFb).

  - type: textarea
    id: system-info
    attributes:
      label: System Info
      description: If needed, you can share your lerobot configuration with us by running `python -m lerobot.scripts.display_sys_info` and copy-pasting its outputs below
      render: Shell
      placeholder: lerobot version, OS, python version, numpy version, torch version, and lerobot's configuration
    validations:
      required: true

  - type: checkboxes
    id: information-scripts-examples
    attributes:
      label: Information
      description: 'The problem arises when using:'
      options:
        - label: "One of the scripts in the examples/ folder of LeRobot"
        - label: "My own task or dataset (give details below)"

  - type: textarea
    id: reproduction
    validations:
      required: true
    attributes:
      label: Reproduction
      description: |
        If needed, provide a simple code sample that reproduces the problem you ran into. It can be a Colab link or just a code snippet.
        Sharing error messages or stack traces could be useful as well!
        Important! Use code tags to correctly format your code. See https://help.github.com/en/github/writing-on-github/creating-and-highlighting-code-blocks#syntax-highlighting
        Try to avoid screenshots, as they are hard to read and don't allow copy-and-pasting.

      placeholder: |
        Steps to reproduce the behavior:

          1.
          2.
          3.

  - type: textarea
    id: expected-behavior
    validations:
      required: true
    attributes:
      label: Expected behavior
      description: "A clear and concise description of what you would expect to happen."
