
"""
A script to relax all connected robot arms (leader and follower).

This script connects to the robot defined by command-line arguments, disables torque on all motors
for both leader and follower arms, and then disconnects.

Usage:
    # Relax a specific robot (e.g. so101) by overriding the default robot type.
    python lerobot/scripts/release_arm.py --robot.type=so101

    # To speed up connection, you can disable cameras.
    python lerobot/scripts/release_arm.py --robot.type=so101 --robot.cameras='{}'
"""
from dataclasses import dataclass, field

from lerobot.common.robot_devices.robots.configs import RobotConfig
from lerobot.common.robot_devices.robots.utils import make_robot_from_config
from lerobot.configs import parser


@dataclass
class ReleaseArmConfig:
    """A simple config that only contains the robot configuration."""

    robot: RobotConfig = field(default_factory=RobotConfig)


@parser.wrap()
def release_arm(cfg: ReleaseArmConfig):
    """
    Connects to the robot, disables torque on all motors, and disconnects.
    """
    robot = make_robot_from_config(cfg.robot)

    print("Connecting to the robot...")
    robot.connect()
    print("Successfully connected to the robot.")

    try:
        if hasattr(robot, "leader_arms") and robot.leader_arms:
            print("Releasing leader arms...")
            for arm_name, arm in robot.leader_arms.items():
                arm.write("Torque_Enable", 0)
                print(f"  - Leader arm '{arm_name}' released.")

        if hasattr(robot, "follower_arms") and robot.follower_arms:
            print("Releasing follower arms...")
            for arm_name, arm in robot.follower_arms.items():
                arm.write("Torque_Enable", 0)
                print(f"  - Follower arm '{arm_name}' released.")

        print("\nAll arms have been released.")

    finally:
        print("Disconnecting from the robot...")
        robot.disconnect()
        print("Successfully disconnected.")


if __name__ == "__main__":
    release_arm() 