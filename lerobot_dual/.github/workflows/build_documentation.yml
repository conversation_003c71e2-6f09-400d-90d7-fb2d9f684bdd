name: Build documentation

on:
  workflow_dispatch:
  push:
    paths:
      - "docs/**"
    branches:
    - main
    - doc-builder*
    - v*-release


jobs:
  build:  # zizmor: ignore[excessive-permissions] We follow the same pattern as in Transformers
    uses: huggingface/doc-builder/.github/workflows/build_main_documentation.yml@main
    with:
      commit_sha: ${{ github.sha }}
      package: lerobot
      additional_args: --not_python_module
    secrets:
      token: ${{ secrets.HUGGINGFACE_PUSH }}
      hf_token: ${{ secrets.HF_DOC_BUILD_PUSH }}
