#!/usr/bin/env python3
"""
Test script to verify that the dtype fix is working correctly.
"""

import sys
import os
sys.path.insert(0, '/data/lerobot_dual_single')
sys.path.insert(0, '/data/lerobot_dual_single/lerobot_dual')

import torch
from lerobot_dual.lerobot.common.policies.pi0.paligemma_with_expert import PaliGemmaWithExpertModel, PaliGemmaWithExpertConfig

def test_dtype_conversion():
    print("Testing dtype conversion...")
    
    # Create a simple config
    config = PaliGemmaWithExpertConfig()
    
    # Create the model
    print("Creating model...")
    model = PaliGemmaWithExpertModel(config)
    
    # Check if vision tower parameters are bfloat16
    print("Checking parameter dtypes...")
    vision_params_found = False
    for name, param in model.named_parameters():
        if 'vision_tower' in name and 'patch_embedding' in name:
            print(f"Vision parameter {name}: {param.dtype}")
            vision_params_found = True
            if param.dtype != torch.bfloat16:
                print(f"ERROR: Parameter {name} is {param.dtype}, expected bfloat16")
                return False
    
    if not vision_params_found:
        print("WARNING: No vision tower patch_embedding parameters found")
    
    # Test embed_image with float32 input
    print("Testing embed_image with float32 input...")
    try:
        # Create a dummy image tensor in float32
        dummy_image = torch.randn(1, 3, 224, 224, dtype=torch.float32, device='cuda')
        print(f"Input image dtype: {dummy_image.dtype}")
        
        # Try to embed the image
        with torch.no_grad():
            result = model.embed_image(dummy_image)
        
        print(f"Output dtype: {result.dtype}")
        print("SUCCESS: embed_image worked without dtype error!")
        return True
        
    except RuntimeError as e:
        if "Input type" in str(e) and "weight type" in str(e):
            print(f"ERROR: Still getting dtype mismatch: {e}")
            return False
        else:
            print(f"Different error (might be expected): {e}")
            return True
    except Exception as e:
        print(f"Other error: {e}")
        return True

if __name__ == "__main__":
    success = test_dtype_conversion()
    if success:
        print("\n✅ Test PASSED: Dtype conversion appears to be working!")
    else:
        print("\n❌ Test FAILED: Dtype conversion is not working correctly.")
    sys.exit(0 if success else 1)
