# LeRobot 远程控制系统详细技术文档

本文档详细描述了LeRobot框架中的远程控制（teleoperate）系统的工作原理、代码实现和执行流程，特别是`control_robot.py`脚本中的远程操作机制。

## 一、脚本系统架构

LeRobot框架包含多个脚本，构成了一个完整的机器人控制、数据采集和模型训练生态系统：

| 脚本文件 | 功能描述 | 技术细节 |
|---------|---------|---------|
| `control_robot.py` | 机器人控制主脚本 | 支持远程操作、数据记录、回放和校准等多种模式 |
| `release_arm.py` | 释放机械臂电机扭矩 | 紧急情况下安全释放机器人关节，防止损坏 |
| `configure_motor.py` | 配置电机参数 | 调整PID参数、最大速度、加速度等底层控制参数 |
| `control_sim_robot.py` | 模拟环境控制 | 在无硬件情况下进行开发和测试 |
| `display_sys_info.py` | 显示系统信息 | 硬件连接状态、版本信息和诊断数据 |
| `eval.py` | 评估策略性能 | 计算成功率、平均回报和其他指标 |
| `find_motors_bus_port.py` | 查找电机端口 | 自动检测并列出可用的电机总线端口 |
| `push_pretrained.py` | 模型分享工具 | 将训练好的模型推送到HuggingFace Hub |
| `train.py` | 策略训练工具 | 实现基于模仿学习和强化学习的训练算法 |
| `visualize_dataset.py` | 数据集可视化 | 实时显示记录的状态、动作和图像数据 |
| `visualize_dataset_html.py` | HTML格式可视化 | 生成交互式网页界面查看数据集 |
| `visualize_image_transforms.py` | 图像变换可视化 | 展示数据增强和预处理效果 |

## 二、远程控制命令执行流程

### 2.1 命令解析与执行入口

当执行以下命令时：

```bash
python lerobot/scripts/control_robot.py \
  --robot.type=so101 \
  --robot.cameras='{}' \
  --control.type=teleoperate
```

系统会按照以下步骤执行：

1. **脚本入口点**：
   - 从`control_robot.py`的`if __name__ == "__main__":`部分开始执行
   - 调用`control_robot()`主函数，该函数被`@parser.wrap()`装饰器修饰

2. **参数解析**：
   - `@parser.wrap()`装饰器使用[draccus](https://github.com/facebookresearch/draccus)库解析参数
   - 将`--robot.type=so101`解析为`cfg.robot.type = "so101"`
   - 将`--robot.cameras='{}'`解析为空摄像头配置`cfg.robot.cameras = {}`
   - 将`--control.type=teleoperate`解析为`cfg.control.type = "teleoperate"`，并创建`TeleoperateControlConfig`实例

3. **配置对象创建**：
   - 根据参数创建`ControlPipelineConfig`实例
   - 包含`RobotConfig`和`ControlConfig`两个主要子配置

### 2.2 机器人实例创建

`control_robot()`函数执行以下初始化步骤：

```python
def control_robot(cfg: ControlPipelineConfig):
    init_logging()
    logging.info(pformat(asdict(cfg)))
    
    robot = make_robot_from_config(cfg.robot)
    
    if isinstance(cfg.control, TeleoperateControlConfig):
        _init_rerun(control_config=cfg.control, session_name="lerobot_control_loop_teleop")
        teleoperate(robot, cfg.control)
        
    # ...其他控制类型处理...
    
    if robot.is_connected:
        robot.disconnect()
```

1. **日志初始化**：
   - 调用`init_logging()`设置日志系统
   - 输出配置信息到日志

2. **机器人实例化**：
   - 调用`make_robot_from_config(cfg.robot)`创建机器人实例：
     ```python
     def make_robot_from_config(config: RobotConfig):
         if isinstance(config, ManipulatorRobotConfig):
             from lerobot.common.robot_devices.robots.manipulator import ManipulatorRobot
             return ManipulatorRobot(config)
         # ...其他类型处理...
     ```
   - 对于So101类型，创建`So101RobotConfig`实例，该类继承自`ManipulatorRobotConfig`
   - 最终创建`ManipulatorRobot`类的实例

3. **可视化初始化**：
   - 调用`_init_rerun()`初始化Rerun可视化系统（如果启用）
   - 配置可视化参数，如内存限制、批处理大小等

### 2.3 远程操作函数执行

当识别为远程操作模式时，执行`teleoperate(robot, cfg.control)`函数：

```python
@safe_disconnect
def teleoperate(robot: Robot, cfg: TeleoperateControlConfig):
    control_loop(
        robot=robot,
        control_time_s=cfg.teleop_time_s,
        fps=cfg.fps,
        teleoperate=True,
        display_data=cfg.display_data,
    )
```

1. **安全断开装饰器**：
   - `@safe_disconnect`装饰器确保在函数执行结束或发生异常时正确断开连接
   - 实现原理是使用`try-finally`块，在`finally`中调用`robot.disconnect()`

2. **控制循环调用**：
   - 调用`control_loop()`函数开始核心控制循环
   - 传入参数：
     - `robot`：机器人实例
     - `control_time_s`：控制时长（可能为无限）
     - `fps`：帧率（如果设置）
     - `teleoperate=True`：启用远程操作模式
     - `display_data`：是否显示数据（取决于用户配置）

## 三、远程控制核心循环详解

### 3.1 控制循环结构

`control_loop()`是机器人控制的核心功能，其简化结构如下：

```python
@safe_stop_image_writer
def control_loop(
    robot,
    control_time_s=None,
    teleoperate=False,
    display_data=False,
    dataset: LeRobotDataset | None = None,
    events=None,
    policy: PreTrainedPolicy = None,
    fps: int | None = None,
    single_task: str | None = None,
):
    if not robot.is_connected:
        robot.connect()
        
    # ...配置和初始化...
    
    timestamp = 0
    start_episode_t = time.perf_counter()
    
    while timestamp < control_time_s:
        start_loop_t = time.perf_counter()
        
        if teleoperate:
            observation, action = robot.teleop_step(record_data=True)
        else:
            observation = robot.capture_observation()
            action = None
            
            if policy is not None:
                # ...策略执行...
                
        # ...数据记录和显示...
        
        if fps is not None:
            dt_s = time.perf_counter() - start_loop_t
            busy_wait(1 / fps - dt_s)
            
        dt_s = time.perf_counter() - start_loop_t
        log_control_info(robot, dt_s, fps=fps)
        
        timestamp = time.perf_counter() - start_episode_t
        if events["exit_early"]:
            events["exit_early"] = False
            break
```

1. **连接检查**：
   - 如果机器人未连接，先调用`robot.connect()`建立连接
   - 连接过程会自动执行校准（如果需要）

2. **时间管理**：
   - 记录开始时间`start_episode_t`
   - 在循环中持续跟踪经过时间`timestamp`
   - 如果`control_time_s`为`None`，则使用`float("inf")`设置为无限时长

3. **主循环**：
   - 在每次迭代开始时记录循环开始时间`start_loop_t`
   - 根据`teleoperate`标志决定使用哪种控制方式
   - 如果设置了`fps`，使用`busy_wait()`函数等待适当的时间以维持稳定帧率

### 3.2 远程操作步骤 (teleop_step) 详解

当`teleoperate=True`时，调用`robot.teleop_step(record_data=True)`方法。这个方法是`ManipulatorRobot`类中的核心功能，其完整实现如下：

```python
def teleop_step(
    self, record_data=False
) -> None | tuple[dict[str, torch.Tensor], dict[str, torch.Tensor]]:
    if not self.is_connected:
        raise RobotDeviceNotConnectedError(
            "ManipulatorRobot is not connected. You need to run `robot.connect()`."
        )

    # 步骤1: 读取leader位置
    leader_pos = {}
    for name in self.leader_arms:
        before_lread_t = time.perf_counter()
        leader_pos[name] = self.leader_arms[name].read("Present_Position")
        leader_pos[name] = torch.from_numpy(leader_pos[name])
        self.logs[f"read_leader_{name}_pos_dt_s"] = time.perf_counter() - before_lread_t

    # 步骤2: 向follower发送目标位置
    follower_goal_pos = {}
    for name in self.follower_arms:
        before_fwrite_t = time.perf_counter()
        goal_pos = leader_pos[name]

        # 步骤2.1: 应用安全限制
        if self.config.max_relative_target is not None:
            present_pos = self.follower_arms[name].read("Present_Position")
            present_pos = torch.from_numpy(present_pos)
            goal_pos = ensure_safe_goal_position(goal_pos, present_pos, self.config.max_relative_target)

        follower_goal_pos[name] = goal_pos

        # 步骤2.2: 发送目标位置
        goal_pos = goal_pos.numpy().astype(np.float32)
        self.follower_arms[name].write("Goal_Position", goal_pos)
        self.logs[f"write_follower_{name}_goal_pos_dt_s"] = time.perf_counter() - before_fwrite_t

    # 如果不需要记录数据，提前返回
    if not record_data:
        return

    # 步骤3: 数据记录（如果启用）
    # 步骤3.1: 读取follower位置
    follower_pos = {}
    for name in self.follower_arms:
        before_fread_t = time.perf_counter()
        follower_pos[name] = self.follower_arms[name].read("Present_Position")
        follower_pos[name] = torch.from_numpy(follower_pos[name])
        self.logs[f"read_follower_{name}_pos_dt_s"] = time.perf_counter() - before_fread_t

    # 步骤3.2: 创建状态向量
    state = []
    for name in self.follower_arms:
        if name in follower_pos:
            state.append(follower_pos[name])
    state = torch.cat(state)

    # 步骤3.3: 创建动作向量
    action = []
    for name in self.follower_arms:
        if name in follower_goal_pos:
            action.append(follower_goal_pos[name])
    action = torch.cat(action)

    # 步骤3.4: 捕获相机图像
    images = {}
    for name in self.cameras:
        before_camread_t = time.perf_counter()
        images[name] = self.cameras[name].async_read()
        images[name] = torch.from_numpy(images[name])
        self.logs[f"read_camera_{name}_dt_s"] = self.cameras[name].logs["delta_timestamp_s"]
        self.logs[f"async_read_camera_{name}_dt_s"] = time.perf_counter() - before_camread_t

    # 步骤3.5: 创建返回字典
    obs_dict, action_dict = {}, {}
    obs_dict["observation.state"] = state
    action_dict["action"] = action
    for name in self.cameras:
        obs_dict[f"observation.images.{name}"] = images[name]

    return obs_dict, action_dict
```

执行流程详解：

1. **读取leader位置**：
   - 遍历所有leader手臂（对于单臂, 通常只有一个名为"main"的手臂）
   - 使用`read("Present_Position")`方法读取当前角度位置
   - 该操作从物理电机读取当前角度值，是实时的机器人状态
   - 将读取的numpy数组转换为PyTorch张量
   - 记录读取时间用于性能分析

2. **设置follower目标位置**：
   - 遍历所有follower手臂（对于单臂, 通常也只有一个名为"main"的手臂）
   - 直接使用对应leader手臂的位置作为follower的目标位置
   - 这一步建立了从用户控制（leader）到机器人执行（follower）的直接映射

3. **安全限制**：
   - 如果配置了`max_relative_target`参数，则应用安全限制
   - 读取follower当前位置
   - 调用`ensure_safe_goal_position`函数限制目标位置的变化幅度
   - 该函数实现如下：
     ```python
     def ensure_safe_goal_position(
         goal_pos: torch.Tensor, present_pos: torch.Tensor, max_relative_target: float | list[float]
     ):
         diff = goal_pos - present_pos  # 计算位置差异
         max_relative_target = torch.tensor(max_relative_target)
         safe_diff = torch.minimum(diff, max_relative_target)  # 限制正向差异
         safe_diff = torch.maximum(safe_diff, -max_relative_target)  # 限制负向差异
         safe_goal_pos = present_pos + safe_diff  # 计算安全目标位置
         
         # 如果进行了限制，发出警告
         if not torch.allclose(goal_pos, safe_goal_pos):
             logging.warning(
                 "Relative goal position magnitude had to be clamped to be safe.\n"
                 f"  requested relative goal position target: {diff}\n"
                 f"    clamped relative goal position target: {safe_diff}"
             )
         
         return safe_goal_pos
     ```

4. **发送目标位置**：
   - 将最终目标位置转回numpy数组
   - 使用`write("Goal_Position", goal_pos)`方法发送给follower电机
   - 这一步实际控制机器人移动到目标位置

5. **数据记录**（如果启用）：
   - 读取follower当前位置，获取机器人实际状态
   - 创建状态向量，包含所有关节的位置
   - 创建动作向量，包含所有关节的目标位置
   - 捕获相机图像（如果有）
   - 整合数据到观察和动作字典，用于记录或训练

### 3.3 安全机制详解

系统实现了多层安全机制，确保机器人操作安全可靠：

1. **动作幅度限制**：
   - `max_relative_target`参数限制每次移动的最大角度变化
   - 这防止了由于用户操作过大或传感器噪声导致的突然大幅度运动
   - 可以为所有电机设置统一限制，或为每个电机单独设置

2. **安全断开机制**：
   - `@safe_disconnect`装饰器确保即使在发生异常时也能正确断开连接
   - 实现方式是在装饰器中使用try-finally块：
     ```python
     def safe_disconnect(func):
         @wraps(func)
         def wrapper(robot, *args, **kwargs):
             try:
                 return func(robot, *args, **kwargs)
             finally:
                 if getattr(robot, "is_connected", False):
                     robot.disconnect()
         return wrapper
     ```

3. **异常处理**：
   - 系统在各个关键点检查连接状态、数据合法性等
   - 发生异常时提供详细的错误信息和处理建议

4. **电机参数调整**：
   - 在`set_so100_robot_preset`和类似函数中设置电机PID参数和加速度限制
   - 降低P系数（P_Coefficient=16，默认为32）减少抖动
   - 设置最大加速度（Maximum_Acceleration=254）实现平滑运动

## 四、硬件与底层通信详解

### 4.1 So101机器人硬件架构

So101机器人是一种操作型机器人，具有以下硬件组成：

1. **机械结构**：
   - 6自由度机械臂
   - 所有关节均使用伺服电机驱动
   - 关节配置：肩部平移(shoulder_pan)、肩部提升(shoulder_lift)、肘部弯曲(elbow_flex)、手腕弯曲(wrist_flex)、手腕旋转(wrist_roll)和夹持器(gripper)

2. **控制系统**：
   - **Leader控制器**：用户直接操作的物理控制器
     - 使用FeetechMotorsBus通过串口与电脑连接
     - 默认串口：`/dev/tty.usbmodem5A680117391`
     - 包含6个STS3215型号伺服电机
   
   - **Follower控制器**：执行实际机器人动作的控制系统
     - 也是通过FeetechMotorsBus连接
     - 默认串口：`/dev/tty.usbmodem5A7A0546671`
     - 同样包含6个STS3215型号伺服电机

3. **传感系统**：
   - 每个电机内置位置和速度传感器
   - 可选的相机系统（在示例命令中被禁用）
     - 默认配置包含"laptop"和"phone"两个相机

### 4.2 电机通信协议

系统使用专用协议与伺服电机通信：

1. **Feetech通信协议**：
   - 使用串行总线（UART/RS485）通信
   - 支持读写多种寄存器，如：
     - `Present_Position`：当前位置
     - `Goal_Position`：目标位置
     - `Mode`：控制模式
     - `P_Coefficient`/`I_Coefficient`/`D_Coefficient`：PID控制参数
     - `Maximum_Acceleration`/`Acceleration`：加速度参数

2. **电机初始化过程**：
   ```python
   def set_so100_robot_preset(self):
       for name in self.follower_arms:
           # Mode=0 表示位置控制模式
           self.follower_arms[name].write("Mode", 0)
           # 降低P系数减少抖动（默认为32）
           self.follower_arms[name].write("P_Coefficient", 16)
           # 设置I和D系数为默认值
           self.follower_arms[name].write("I_Coefficient", 0)
           self.follower_arms[name].write("D_Coefficient", 32)
           # 关闭写锁定，使配置写入EPROM
           self.follower_arms[name].write("Lock", 0)
           # 设置最大加速度以提高响应速度
           self.follower_arms[name].write("Maximum_Acceleration", 254)
           self.follower_arms[name].write("Acceleration", 254)
   ```

3. **通信时序**：
   - 系统记录各操作的时间性能
   - 使用`time.perf_counter()`精确测量通信延迟
   - 各环节延迟被记录到`self.logs`字典中，用于调试和性能优化

### 4.3 数据表示与处理

系统使用以下数据结构表示机器人状态和动作：

1. **位置数据**：
   - 使用PyTorch张量表示关节角度
   - 对于So101，每个手臂有6个浮点数表示6个关节的角度
   - 单位为度（degree），范围因电机型号而异

2. **观察数据**：
   - 状态向量：包含所有关节的位置
   - 图像数据（如果启用）：来自相机的RGB图像
   - 格式：字典结构，键为特征名称，值为张量

3. **动作数据**：
   - 目标位置向量：包含所有关节的目标位置
   - 格式：与状态向量相同的张量结构

## 五、高级功能与扩展应用

### 5.1 数据记录与数据集生成

系统支持高质量数据集的记录，用于训练模型：

1. **数据收集过程**：
   - 调用`control_robot.py`的`record`模式
   - 支持设置回放时间、重置时间、帧率等参数
   - 可指定任务描述，用于后续训练

2. **数据结构**：
   - 使用`LeRobotDataset`类组织数据
   - 支持HuggingFace数据集格式，便于分享和复用
   - 包含状态、动作、图像和任务描述

3. **示例命令**：
   ```bash
   python lerobot/scripts/control_robot.py \
       --robot.type=so101 \
       --control.type=record \
       --control.fps=30 \
       --control.single_task="Grasp an object" \
       --control.repo_id=$USER/so101_demo \
       --control.num_episodes=10 \
       --control.push_to_hub=true
   ```

### 5.2 策略训练与评估

基于收集的数据集，系统支持训练控制策略：

1. **训练流程**：
   - 使用`train.py`脚本训练模型
   - 支持多种策略类型，如模仿学习、强化学习等
   - 可配置设备、批量大小、学习率等超参数

2. **评估方法**：
   - 使用`eval.py`脚本评估策略性能
   - 可计算成功率、平均回报等指标
   - 支持生成评估视频和详细日志

3. **策略部署**：
   - 使用`control_robot.py`的策略执行模式运行训练好的策略
   - 可实时控制机器人执行任务
   - 支持记录评估数据集，用于持续改进

### 5.3 系统扩展与自定义

框架设计支持多种扩展和自定义方式：

1. **新机器人类型**：
   - 继承`ManipulatorRobotConfig`或`RobotConfig`创建新配置类
   - 注册到配置注册表：`@RobotConfig.register_subclass("your_robot")`
   - 实现必要的硬件通信接口

2. **自定义控制器**：
   - 创建新的控制配置类，如`YourControlConfig`
   - 实现对应的控制函数
   - 在`control_robot.py`中添加新的控制模式

3. **集成第三方硬件**：
   - 实现相应的相机、电机总线和其他设备类
   - 通过配置系统注册和使用

## 六、常见问题与故障排除

1. **连接问题**：
   - 检查USB端口名称是否正确
   - 使用`find_motors_bus_port.py`脚本查找可用端口
   - 检查电机总线的供电和通信设置

2. **校准问题**：
   - 如果机器人运动不正确，可能需要重新校准
   - 使用`--control.type=calibrate`模式执行校准
   - 校准文件存储在配置指定的校准目录

3. **安全停止**：
   - 紧急情况下使用`release_arm.py`释放电机扭矩
   - 按Ctrl+C可安全中断控制循环
   - 系统会自动执行安全断开流程

4. **性能优化**：
   - 调整PID参数可改变响应特性
   - 修改`max_relative_target`可平衡安全性和响应性
   - 使用`display_sys_info.py`监控系统性能

## 七、命令参考

### 7.1 远程操作命令

基本远程操作命令：
```bash
python lerobot/scripts/control_robot.py \
  --robot.type=so101 \
  --control.type=teleoperate
```

带摄像头的远程操作：
```bash
python lerobot/scripts/control_robot.py \
  --robot.type=so101 \
  --control.type=teleoperate \
  --control.display_data=true
```

限制帧率的远程操作：
```bash
python lerobot/scripts/control_robot.py \
  --robot.type=so101 \
  --control.type=teleoperate \
  --control.fps=30
```

### 7.2 校准命令

校准机器人：
```bash
python lerobot/scripts/control_robot.py \
  --robot.type=so101 \
  --control.type=calibrate
```

校准特定手臂：
```bash
python lerobot/scripts/control_robot.py \
  --robot.type=so101 \
  --control.type=calibrate \
  --control.arms="main_follower main_leader"
```

### 7.3 数据记录命令

记录训练数据集：
```bash
python lerobot/scripts/control_robot.py \
  --robot.type=so101 \
  --control.type=record \
  --control.fps=30 \
  --control.single_task="Pick up the block" \
  --control.repo_id=$USER/so101_training \
  --control.num_episodes=50 \
  --control.warmup_time_s=2 \
  --control.episode_time_s=30 \
  --control.reset_time_s=10 \
  --control.push_to_hub=true
```

回放记录的数据：
```bash
python lerobot/scripts/control_robot.py \
  --robot.type=so101 \
  --control.type=replay \
  --control.repo_id=$USER/so101_training \
  --control.episode=0
```

## 八、总结

LeRobot的远程控制系统提供了一种强大、灵活且安全的方式来操作机器人，收集训练数据和部署智能控制策略。其核心机制是通过leader-follower架构，将用户输入直接映射到机器人动作，并应用安全限制确保操作安全。系统的模块化设计使其易于扩展到新的机器人类型和控制模式，而完整的数据收集和训练工具链支持端到端的机器人学习应用开发。 